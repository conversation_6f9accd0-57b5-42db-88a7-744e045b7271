{"name": "Reixi.js", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"dev": "ts-node-dev --esm server.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.14.0", "dependencies": {"express": "^5.1.0", "react": "^19.1.1", "react-dom": "^19.1.1"}, "devDependencies": {"ts-node-dev": "^2.0.0"}}