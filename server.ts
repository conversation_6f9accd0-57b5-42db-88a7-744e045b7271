import express from 'express';
import React from 'react';
import { renderToString } from 'react-dom/server';
import App from './src/App.jsx';

const app = express();

// 提供静态资源 (打包后的客户端代码)
app.use(express.static('public'));

app.get('*', (req, res) => {
  const appHTML = renderToString(<App />);

  const html = `
    <!DOCTYPE html>
    <html>
      <head>
        <title>React SSR Demo</title>
      </head>
      <body>
        <div id="root">${appHTML}</div>
        <script src="/client.js"></script>
      </body>
    </html>
  `;
  res.send(html);
});

app.listen(3000, () => {
  console.log('SSR server running at http://localhost:3000');
});